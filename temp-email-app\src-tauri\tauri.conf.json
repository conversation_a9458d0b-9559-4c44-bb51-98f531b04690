{"$schema": "https://schema.tauri.app/config/2", "productName": "temp-email-app", "version": "0.1.0", "identifier": "com.temp-email-app.app", "build": {"beforeDevCommand": "npm run dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "npm run build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "临时邮箱应用", "width": 500, "height": 400, "resizable": false, "center": true}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}