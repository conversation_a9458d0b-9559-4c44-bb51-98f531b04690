# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-identifier"
description = "Enables the identifier command without any pre-configured scope."
commands.allow = ["identifier"]

[[permission]]
identifier = "deny-identifier"
description = "Denies the identifier command without any pre-configured scope."
commands.deny = ["identifier"]
