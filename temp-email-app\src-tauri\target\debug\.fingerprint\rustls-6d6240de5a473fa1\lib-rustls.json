{"rustc": 1842507548689473721, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 1677134433092527515, "path": 17852916565147195149, "deps": [[1542112352204983347, "build_script_build", false, 9448167694462175998], [2883436298747778685, "pki_types", false, 7785388080773837983], [3722963349756955755, "once_cell", false, 14521976164538839542], [5491919304041016563, "ring", false, 3507238915162128141], [6528079939221783635, "zeroize", false, 6542258275864557609], [8151164558401866693, "<PERSON><PERSON><PERSON>", false, 10819482651665401524], [17003143334332120809, "subtle", false, 1896292488136929212]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rustls-6d6240de5a473fa1\\dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}