{"rustc": 1842507548689473721, "features": "[\"charset\", \"cookies\", \"default\", \"http2\", \"macos-system-configuration\", \"rustls-tls\"]", "declared_features": "[\"blocking\", \"brotli\", \"charset\", \"cookies\", \"dangerous-settings\", \"default\", \"deflate\", \"gzip\", \"http2\", \"json\", \"macos-system-configuration\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-vendored\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"socks\", \"stream\", \"tracing\", \"trust-dns\", \"unsafe-headers\", \"zstd\"]", "target": 7795770796983219439, "profile": 15657897354478470176, "path": 10629928795789213583, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 3713524314323759801], [3150220818285335163, "url", false, 7317158130638989969], [7085222851776090619, "reqwest", false, 2508905311030878591], [8298091525883606470, "cookie_store", false, 6793560136650626855], [9010263965687315507, "http", false, 15629939712500996649], [9451456094439810778, "regex", false, 17848222803548847115], [9689903380558560274, "serde", false, 14326913226427773042], [10806645703491011684, "thiserror", false, 6923350312884292379], [12092653563678505622, "tauri", false, 7265162934273576958], [12504415026414629397, "tauri_plugin_fs", false, 6711310137324824110], [14909000976169095833, "build_script_build", false, 16372603295684532039], [16066129441945555748, "bytes", false, 2626033548966229242], [16362055519698394275, "serde_json", false, 3204649667060129030], [17047088963840213854, "data_url", false, 6232834256312602976], [17531218394775549125, "tokio", false, 1088390662713067021]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-plugin-http-30878a1da383d5ae\\dep-lib-tauri_plugin_http", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}