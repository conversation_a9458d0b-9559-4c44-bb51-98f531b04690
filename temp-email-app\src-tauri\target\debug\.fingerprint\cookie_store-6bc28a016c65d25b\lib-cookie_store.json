{"rustc": 1842507548689473721, "features": "[\"default\", \"public_suffix\", \"serde\", \"serde_json\"]", "declared_features": "[\"default\", \"log_secure_cookie_values\", \"preserve_order\", \"public_suffix\", \"serde\", \"serde_json\", \"serde_ron\", \"wasm-bindgen\"]", "target": 8140962409157740669, "profile": 15657897354478470176, "path": 17266973371716140037, "deps": [[505596520502798227, "publicsuffix", false, 17808447804655218615], [3150220818285335163, "url", false, 7317158130638989969], [5986029879202738730, "log", false, 5047229584628730050], [6376232718484714452, "idna", false, 14353533402135624463], [9689903380558560274, "serde", false, 14326913226427773042], [11763018104473073732, "document_features", false, 2543317790450458861], [12409575957772518135, "time", false, 11361612295999103649], [16257276029081467297, "serde_derive", false, 5518710217882152980], [16362055519698394275, "serde_json", false, 3204649667060129030], [16727543399706004146, "cookie", false, 17158682527140028858]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\cookie_store-6bc28a016c65d25b\\dep-lib-cookie_store", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}