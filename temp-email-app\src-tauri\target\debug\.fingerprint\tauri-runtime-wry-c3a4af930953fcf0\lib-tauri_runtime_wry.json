{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 8261735638378605462, "deps": [[376837177317575824, "softbuffer", false, 5768703112320592620], [654232091421095663, "tauri_utils", false, 3432721596253274002], [2013030631243296465, "webview2_com", false, 11257737392710050888], [3150220818285335163, "url", false, 7317158130638989969], [3722963349756955755, "once_cell", false, 14521976164538839542], [4143744114649553716, "raw_window_handle", false, 14178324387943116473], [5986029879202738730, "log", false, 5047229584628730050], [8826339825490770380, "tao", false, 15709087323731223105], [9010263965687315507, "http", false, 15629939712500996649], [9141053277961803901, "wry", false, 10107979758729159677], [12304025191202589669, "build_script_build", false, 14405279426092763355], [12943761728066819757, "tauri_runtime", false, 8288498641022886286], [14585479307175734061, "windows", false, 13749503539899060250]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-c3a4af930953fcf0\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}