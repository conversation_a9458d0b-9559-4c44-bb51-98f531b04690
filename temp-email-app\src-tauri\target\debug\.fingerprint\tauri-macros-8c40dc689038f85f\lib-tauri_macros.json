{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 17481861598492978825, "deps": [[654232091421095663, "tauri_utils", false, 11209538966929550227], [2704937418414716471, "tauri_codegen", false, 9845880604116978993], [3060637413840920116, "proc_macro2", false, 5373005460213438867], [4974441333307933176, "syn", false, 12980184784408199871], [13077543566650298139, "heck", false, 12227043807642275620], [17990358020177143287, "quote", false, 9227116837396871820]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-8c40dc689038f85f\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}