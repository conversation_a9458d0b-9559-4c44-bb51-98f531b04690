{"rustc": 1842507548689473721, "features": "[\"http1\", \"http2\", \"ring\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "declared_features": "[\"aws-lc-rs\", \"default\", \"fips\", \"http1\", \"http2\", \"log\", \"logging\", \"native-tokio\", \"ring\", \"rustls-native-certs\", \"rustls-platform-verifier\", \"tls12\", \"webpki-roots\", \"webpki-tokio\"]", "target": 12220062926890100908, "profile": 15657897354478470176, "path": 632669836025513150, "deps": [[41016358116313498, "hyper_util", false, 2975471783397851694], [784494742817713399, "tower_service", false, 11660862420847627360], [1542112352204983347, "rustls", false, 314683210447608114], [2883436298747778685, "pki_types", false, 7785388080773837983], [8153991275959898788, "webpki_roots", false, 5075741082113052990], [9010263965687315507, "http", false, 15629939712500996649], [11895591994124935963, "tokio_rustls", false, 11112055842640328575], [11957360342995674422, "hyper", false, 15149955344844057402], [17531218394775549125, "tokio", false, 1088390662713067021]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\hyper-rustls-32fa7d98c00b1b7c\\dep-lib-hyper_rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}