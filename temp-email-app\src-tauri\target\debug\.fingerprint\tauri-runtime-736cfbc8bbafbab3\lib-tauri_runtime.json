{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 451249050788002760, "deps": [[654232091421095663, "tauri_utils", false, 3432721596253274002], [3150220818285335163, "url", false, 7317158130638989969], [4143744114649553716, "raw_window_handle", false, 14178324387943116473], [7606335748176206944, "dpi", false, 10125233705909480676], [9010263965687315507, "http", false, 15629939712500996649], [9689903380558560274, "serde", false, 14326913226427773042], [10806645703491011684, "thiserror", false, 6923350312884292379], [12943761728066819757, "build_script_build", false, 14763020576908811479], [14585479307175734061, "windows", false, 13749503539899060250], [16362055519698394275, "serde_json", false, 3204649667060129030], [16727543399706004146, "cookie", false, 17158682527140028858]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-736cfbc8bbafbab3\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}