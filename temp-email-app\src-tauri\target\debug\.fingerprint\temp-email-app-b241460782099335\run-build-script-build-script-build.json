{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12092653563678505622, "build_script_build", false, 1004696179973834854], [1797035611096599003, "build_script_build", false, 5466770975093912362], [14909000976169095833, "build_script_build", false, 16372603295684532039], [2784153353110520258, "build_script_build", false, 769189936618119377], [16199841968381804945, "build_script_build", false, 10659358371528268449]], "local": [{"RerunIfChanged": {"output": "debug\\build\\temp-email-app-b241460782099335\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}