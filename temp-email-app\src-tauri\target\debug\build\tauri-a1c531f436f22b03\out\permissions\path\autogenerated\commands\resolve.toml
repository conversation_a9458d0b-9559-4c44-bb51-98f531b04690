# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-resolve"
description = "Enables the resolve command without any pre-configured scope."
commands.allow = ["resolve"]

[[permission]]
identifier = "deny-resolve"
description = "Denies the resolve command without any pre-configured scope."
commands.deny = ["resolve"]
