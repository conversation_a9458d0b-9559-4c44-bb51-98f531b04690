<script setup lang="ts">
import { ref, onMounted } from "vue";
import { invoke } from "@tauri-apps/api/core";
import { writeText } from "@tauri-apps/plugin-clipboard-manager";

// 响应式数据
const username = ref("");
const domains = ref<Array<{id: number, domain: string}>>([]);
const selectedDomainId = ref<number | null>(null);
const currentEmail = ref("");
const isLoading = ref(false);
const message = ref("");
const autoGetCode = ref(false);
const isAutoGetting = ref(false);

// 生成随机用户名
async function generateUsername() {
  try {
    username.value = await invoke("generate_random_username");
  } catch (error) {
    console.error("生成用户名失败:", error);
    message.value = "生成用户名失败";
  }
}

// 获取域名列表
async function loadDomains() {
  try {
    const domainList = await invoke("get_domains");
    domains.value = domainList as Array<{id: number, domain: string}>;
    if (domains.value.length > 0) {
      selectedDomainId.value = domains.value[0].id;
    }
  } catch (error) {
    console.error("获取域名列表失败:", error);
    message.value = "获取域名列表失败: " + error;
  }
}

// 创建邮箱
async function createEmail() {
  if (!username.value || !selectedDomainId.value) {
    message.value = "请输入用户名并选择域名";
    return;
  }

  isLoading.value = true;
  message.value = "";

  try {
    const email = await invoke("create_email", {
      name: username.value,
      domainId: selectedDomainId.value
    });
    currentEmail.value = email as string;

    // 复制到剪贴板
    await writeText(currentEmail.value);
    message.value = "邮箱创建成功并已复制到剪贴板！";

    // 如果开启自动获取验证码，开始轮询
    if (autoGetCode.value) {
      startAutoGetCode();
    }
  } catch (error) {
    console.error("创建邮箱失败:", error);
    message.value = "创建邮箱失败: " + error;
  } finally {
    isLoading.value = false;
  }
}

// 手动获取验证码
async function getVerificationCode() {
  if (!currentEmail.value) {
    message.value = "请先创建邮箱";
    return;
  }

  isLoading.value = true;
  message.value = "正在获取验证码...";

  try {
    const messages = await invoke("get_messages", { email: currentEmail.value });
    const messageList = messages as Array<{id: number, subject: string, messageId: string}>;

    if (messageList.length === 0) {
      message.value = "暂无邮件";
      return;
    }

    // 获取最新邮件的验证码
    const latestMessage = messageList[0];
    const code = await invoke("get_verification_code", { messageId: latestMessage.messageId });

    // 复制验证码到剪贴板
    await writeText(code as string);
    message.value = `验证码: ${code} (已复制到剪贴板)`;
  } catch (error) {
    console.error("获取验证码失败:", error);
    message.value = "获取验证码失败: " + error;
  } finally {
    isLoading.value = false;
  }
}

// 自动获取验证码
let autoGetInterval: number | null = null;

function startAutoGetCode() {
  if (autoGetInterval) {
    clearInterval(autoGetInterval);
  }

  isAutoGetting.value = true;
  message.value = "自动获取验证码已开启，每5秒检查一次...";

  autoGetInterval = setInterval(async () => {
    try {
      const messages = await invoke("get_messages", { email: currentEmail.value });
      const messageList = messages as Array<{id: number, subject: string, messageId: string}>;

      if (messageList.length > 0) {
        const latestMessage = messageList[0];
        const code = await invoke("get_verification_code", { messageId: latestMessage.messageId });

        // 复制验证码到剪贴板
        await writeText(code as string);
        message.value = `自动获取到验证码: ${code} (已复制到剪贴板)`;

        // 停止自动获取
        stopAutoGetCode();
      }
    } catch (error) {
      console.error("自动获取验证码失败:", error);
    }
  }, 5000);
}

function stopAutoGetCode() {
  if (autoGetInterval) {
    clearInterval(autoGetInterval);
    autoGetInterval = null;
  }
  isAutoGetting.value = false;
}

// 切换自动获取验证码
function toggleAutoGetCode() {
  if (autoGetCode.value && currentEmail.value) {
    startAutoGetCode();
  } else {
    stopAutoGetCode();
  }
}

// 复制邮箱到剪贴板
async function copyEmail() {
  if (currentEmail.value) {
    try {
      await writeText(currentEmail.value);
      message.value = "邮箱已复制到剪贴板！";
    } catch (error) {
      console.error("复制失败:", error);
      message.value = "复制失败";
    }
  }
}

// 组件挂载时初始化
onMounted(async () => {
  await loadDomains();
  await generateUsername();
});
</script>

<template>
  <div class="app">
    <div class="container">
      <!-- 头部标题 -->
      <div class="header">
        <div class="title">
          <div class="icon">📧</div>
          <h1>临时邮箱</h1>
        </div>
        <p class="subtitle">快速生成临时邮箱，获取验证码</p>
      </div>

      <!-- 邮箱生成卡片 -->
      <div class="card email-card">
        <div class="card-header">
          <h2>📮 生成邮箱</h2>
        </div>
        <div class="card-content">
          <div class="input-group">
            <div class="email-builder">
              <input
                v-model="username"
                placeholder="输入用户名"
                class="username-input"
                :disabled="isLoading"
              />
              <span class="at-symbol">@</span>
              <select
                v-model="selectedDomainId"
                class="domain-select"
                :disabled="isLoading"
              >
                <option v-for="domain in domains" :key="domain.id" :value="domain.id">
                  {{ domain.domain }}
                </option>
              </select>
            </div>
            <button
              @click="createEmail"
              class="primary-btn"
              :disabled="isLoading || !username || !selectedDomainId"
            >
              <span v-if="isLoading" class="loading-spinner"></span>
              {{ isLoading ? '生成中...' : '生成邮箱' }}
            </button>
          </div>

          <div v-if="currentEmail" class="current-email">
            <div class="email-display">
              <span class="email-text">{{ currentEmail }}</span>
              <button @click="copyEmail" class="copy-btn" title="复制邮箱">
                📋
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 验证码获取卡片 -->
      <div class="card code-card">
        <div class="card-header">
          <h2>🔐 获取验证码</h2>
        </div>
        <div class="card-content">
          <div class="code-controls">
            <button
              @click="getVerificationCode"
              class="secondary-btn"
              :disabled="isLoading || !currentEmail"
            >
              <span v-if="isLoading" class="loading-spinner"></span>
              {{ isLoading ? '获取中...' : '获取验证码' }}
            </button>

            <div class="auto-toggle">
              <label class="switch">
                <input
                  type="checkbox"
                  v-model="autoGetCode"
                  @change="toggleAutoGetCode"
                  :disabled="!currentEmail"
                />
                <span class="slider"></span>
              </label>
              <span class="toggle-label">
                自动获取
                <span v-if="isAutoGetting" class="status-indicator">●</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 状态消息 -->
      <div v-if="message" class="message-card" :class="{ 'error': message.includes('失败') }">
        <div class="message-content">
          <span class="message-icon">{{ message.includes('失败') ? '❌' : '✅' }}</span>
          <span class="message-text">{{ message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.app {
  min-height: 100vh;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

.container {
  max-width: 500px;
  margin: 0 auto;
  padding: 24px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 头部样式 */
.header {
  text-align: center;
  margin-bottom: 8px;
}

.title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 8px;
}

.icon {
  font-size: 32px;
}

h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  letter-spacing: -0.5px;
}

.subtitle {
  margin: 0;
  color: #666;
  font-size: 14px;
  font-weight: 400;
}

/* 卡片样式 */
.card {
  background: #ffffff;
  border-radius: 16px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: all 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: #d1d5db;
}

.card-header {
  padding: 20px 24px 0;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-content {
  padding: 20px 24px 24px;
}

/* 输入组样式 */
.input-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.email-builder {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.email-builder:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.username-input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #1f2937;
  outline: none;
}

.username-input::placeholder {
  color: #9ca3af;
}

.at-symbol {
  font-weight: 600;
  color: #6b7280;
  font-size: 16px;
}

.domain-select {
  flex: 1;
  padding: 12px 16px;
  border: none;
  background: transparent;
  font-size: 16px;
  color: #1f2937;
  outline: none;
  cursor: pointer;
}

/* 按钮样式 */
.primary-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 24px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 52px;
}

.primary-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.primary-btn:disabled {
  background: #e5e7eb;
  color: #9ca3af;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.secondary-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.secondary-btn:hover:not(:disabled) {
  background: #e5e7eb;
  border-color: #9ca3af;
}

.secondary-btn:disabled {
  background: #f9fafb;
  color: #d1d5db;
  cursor: not-allowed;
}

/* 加载动画 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 邮箱显示 */
.current-email {
  margin-top: 16px;
}

.email-display {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border: 1px solid #bae6fd;
  border-radius: 12px;
}

.email-text {
  flex: 1;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 14px;
  font-weight: 500;
  color: #0c4a6e;
  word-break: break-all;
}

.copy-btn {
  padding: 8px;
  background: #0ea5e9;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.copy-btn:hover {
  background: #0284c7;
  transform: scale(1.05);
}

/* 验证码控制 */
.code-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.auto-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e5e7eb;
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

input:checked + .slider {
  background-color: #3b82f6;
}

input:checked + .slider:before {
  transform: translateX(24px);
}

input:disabled + .slider {
  opacity: 0.5;
  cursor: not-allowed;
}

.toggle-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-indicator {
  color: #10b981;
  font-size: 12px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 消息卡片 */
.message-card {
  padding: 16px;
  border-radius: 12px;
  border: 1px solid #d1fae5;
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.message-card.error {
  border-color: #fecaca;
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
}

.message-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.message-icon {
  font-size: 18px;
}

.message-text {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #065f46;
}

.message-card.error .message-text {
  color: #991b1b;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .container {
    padding: 16px;
  }

  .card-content {
    padding: 16px 20px 20px;
  }

  .email-builder {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 16px;
  }

  .at-symbol {
    display: none;
  }

  .code-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .auto-toggle {
    justify-content: center;
  }
}
</style>