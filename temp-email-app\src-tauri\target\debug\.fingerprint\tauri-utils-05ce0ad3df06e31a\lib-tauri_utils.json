{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 14215436630019529674, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 3713524314323759801], [3150220818285335163, "url", false, 7317158130638989969], [3191507132440681679, "serde_untagged", false, 1603952148174892517], [4071963112282141418, "serde_with", false, 17872481161011685680], [4899080583175475170, "semver", false, 8957010987447148668], [5986029879202738730, "log", false, 5047229584628730050], [6606131838865521726, "ctor", false, 8001462743388477718], [7170110829644101142, "json_patch", false, 2182638515818055219], [8319709847752024821, "uuid", false, 4488454444859956651], [9010263965687315507, "http", false, 15629939712500996649], [9090328626728818999, "toml", false, 5232494769957618302], [9451456094439810778, "regex", false, 17848222803548847115], [9556762810601084293, "brotli", false, 4284906626722745546], [9689903380558560274, "serde", false, 14326913226427773042], [10806645703491011684, "thiserror", false, 6923350312884292379], [11989259058781683633, "dunce", false, 11426863789755588728], [13625485746686963219, "anyhow", false, 16521415306001099308], [15622660310229662834, "walkdir", false, 15547483926380054014], [15932120279885307830, "memchr", false, 10200081116297473958], [16362055519698394275, "serde_json", false, 3204649667060129030], [17146114186171651583, "infer", false, 12939566430890367068], [17155886227862585100, "glob", false, 16666287207728800018], [17186037756130803222, "phf", false, 4062280988673869342]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-05ce0ad3df06e31a\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}