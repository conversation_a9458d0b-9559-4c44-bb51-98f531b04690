{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 5726785450906783363, "deps": [[654232091421095663, "tauri_utils", false, 11209538966929550227], [3060637413840920116, "proc_macro2", false, 5373005460213438867], [3150220818285335163, "url", false, 11457643946002643913], [4899080583175475170, "semver", false, 16636169827845809847], [4974441333307933176, "syn", false, 12980184784408199871], [7170110829644101142, "json_patch", false, 11884101678998366027], [7392050791754369441, "ico", false, 17313293278570163110], [8319709847752024821, "uuid", false, 17523704281557203773], [9556762810601084293, "brotli", false, 4284906626722745546], [9689903380558560274, "serde", false, 14955956804811053645], [9857275760291862238, "sha2", false, 16345863688798390429], [10806645703491011684, "thiserror", false, 6923350312884292379], [12687914511023397207, "png", false, 4095324035002590683], [13077212702700853852, "base64", false, 17250583037651168056], [15622660310229662834, "walkdir", false, 4450029676203649764], [16362055519698394275, "serde_json", false, 15741321141703459404], [17990358020177143287, "quote", false, 9227116837396871820]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-05daf8469e723732\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}