<script setup lang="ts">
import { ref, onMounted } from "vue";
import { invoke } from "@tauri-apps/api/core";
import { writeText } from "@tauri-apps/plugin-clipboard-manager";
import { Mail, RefreshCw, Copy, Shield, CheckCircle, AlertCircle } from "lucide-vue-next";

// 响应式数据
const username = ref("");
const domains = ref<Array<{id: number, domain: string}>>([]);
const selectedDomainId = ref<number | null>(null);
const currentEmail = ref("");
const isLoading = ref(false);
const message = ref("");
const autoGetCode = ref(false);
const isAutoGetting = ref(false);
const lastCode = ref("");

// 生成随机用户名
async function generateUsername() {
  try {
    username.value = await invoke("generate_random_username");
  } catch (error) {
    console.error("生成用户名失败:", error);
    message.value = "生成用户名失败";
  }
}

// 获取域名列表
async function loadDomains() {
  try {
    const domainList = await invoke("get_domains");
    domains.value = domainList as Array<{id: number, domain: string}>;
    if (domains.value.length > 0) {
      selectedDomainId.value = domains.value[0].id;
    }
  } catch (error) {
    console.error("获取域名列表失败:", error);
    message.value = "获取域名列表失败: " + error;
  }
}

// 创建邮箱
async function createEmail() {
  if (!username.value || !selectedDomainId.value) {
    message.value = "请输入用户名并选择域名";
    return;
  }

  isLoading.value = true;
  message.value = "";

  try {
    const email = await invoke("create_email", {
      name: username.value,
      domainId: selectedDomainId.value
    });
    currentEmail.value = email as string;
    
    // 复制到剪贴板
    await writeText(currentEmail.value);
    message.value = "邮箱创建成功并已复制到剪贴板！";
    
    // 如果开启自动获取验证码，开始轮询
    if (autoGetCode.value) {
      startAutoGetCode();
    }
  } catch (error) {
    console.error("创建邮箱失败:", error);
    message.value = "创建邮箱失败: " + error;
  } finally {
    isLoading.value = false;
  }
}

// 手动获取验证码
async function getVerificationCode() {
  if (!currentEmail.value) {
    message.value = "请先创建邮箱";
    return;
  }

  isLoading.value = true;
  message.value = "正在获取验证码...";

  try {
    const messages = await invoke("get_messages", { email: currentEmail.value });
    const messageList = messages as Array<{id: number, subject: string, messageId: string}>;
    
    if (messageList.length === 0) {
      message.value = "暂无邮件";
      return;
    }

    // 获取最新邮件的验证码
    const latestMessage = messageList[0];
    const code = await invoke("get_verification_code", { messageId: latestMessage.messageId });
    
    // 保存验证码并复制到剪贴板
    lastCode.value = code as string;
    await writeText(code as string);
    message.value = `验证码已获取并复制到剪贴板`;
  } catch (error) {
    console.error("获取验证码失败:", error);
    message.value = "获取验证码失败: " + error;
  } finally {
    isLoading.value = false;
  }
}

// 自动获取验证码
let autoGetInterval: number | null = null;

function startAutoGetCode() {
  if (autoGetInterval) {
    clearInterval(autoGetInterval);
  }
  
  isAutoGetting.value = true;
  message.value = "自动获取验证码已开启，每5秒检查一次...";
  
  autoGetInterval = setInterval(async () => {
    try {
      const messages = await invoke("get_messages", { email: currentEmail.value });
      const messageList = messages as Array<{id: number, subject: string, messageId: string}>;
      
      if (messageList.length > 0) {
        const latestMessage = messageList[0];
        const code = await invoke("get_verification_code", { messageId: latestMessage.messageId });
        
        // 保存验证码并复制到剪贴板
        lastCode.value = code as string;
        await writeText(code as string);
        message.value = `自动获取到验证码并已复制到剪贴板`;
        
        // 停止自动获取
        stopAutoGetCode();
      }
    } catch (error) {
      console.error("自动获取验证码失败:", error);
    }
  }, 5000);
}

function stopAutoGetCode() {
  if (autoGetInterval) {
    clearInterval(autoGetInterval);
    autoGetInterval = null;
  }
  isAutoGetting.value = false;
}

// 切换自动获取验证码
function toggleAutoGetCode() {
  if (autoGetCode.value && currentEmail.value) {
    startAutoGetCode();
  } else {
    stopAutoGetCode();
  }
}

// 复制邮箱到剪贴板
async function copyEmail() {
  if (currentEmail.value) {
    try {
      await writeText(currentEmail.value);
      message.value = "邮箱已复制到剪贴板！";
    } catch (error) {
      console.error("复制失败:", error);
      message.value = "复制失败";
    }
  }
}

// 复制验证码到剪贴板
async function copyCode() {
  if (lastCode.value) {
    try {
      await writeText(lastCode.value);
      message.value = "验证码已复制到剪贴板！";
    } catch (error) {
      console.error("复制失败:", error);
      message.value = "复制失败";
    }
  }
}

// 组件挂载时初始化
onMounted(async () => {
  await loadDomains();
  await generateUsername();
});
</script>

<template>
  <div class="app">
    <!-- 顶部域名选择 -->
    <div class="top-bar">
      <select 
        v-model="selectedDomainId" 
        class="domain-dropdown"
        :disabled="isLoading"
      >
        <option v-for="domain in domains" :key="domain.id" :value="domain.id">
          {{ domain.domain }}
        </option>
      </select>
    </div>

    <div class="container">
      <!-- 邮箱显示框 -->
      <div class="email-box">
        <div class="box-label">邮箱:</div>
        <div class="box-content">
          <span class="email-text">{{ currentEmail || '点击获取邮箱按钮生成' }}</span>
          <button 
            v-if="currentEmail" 
            @click="copyEmail" 
            class="copy-btn" 
            title="复制邮箱"
          >
            <Copy :size="16" />
          </button>
        </div>
        <button 
          @click="createEmail" 
          class="action-btn get-email-btn"
          :disabled="isLoading || !selectedDomainId"
        >
          <span v-if="isLoading" class="loading-spinner"></span>
          {{ isLoading ? '获取中...' : '获取邮箱' }}
        </button>
      </div>

      <!-- 验证码显示框 -->
      <div class="code-box">
        <div class="box-label">
          验证码: 
          <span v-if="lastCode">{{ lastCode }}</span>
        </div>
        <div class="box-content">
          <button 
            v-if="lastCode" 
            @click="copyCode" 
            class="copy-btn" 
            title="复制验证码"
          >
            <Copy :size="16" />
          </button>
        </div>
        <button 
          @click="getVerificationCode" 
          class="action-btn get-code-btn"
          :disabled="isLoading || !currentEmail"
        >
          <span v-if="isLoading" class="loading-spinner"></span>
          <RefreshCw v-else :size="16" />
          {{ isLoading ? '获取中...' : '获取验证码' }}
        </button>
      </div>

      <!-- 自动获取开关 -->
      <div class="auto-toggle-container">
        <label class="auto-toggle">
          <input 
            type="checkbox" 
            v-model="autoGetCode" 
            @change="toggleAutoGetCode"
            :disabled="!currentEmail"
          />
          <span class="toggle-text">自动获取验证码</span>
          <span v-if="isAutoGetting" class="status-indicator">
            <div class="pulse-dot"></div>
          </span>
        </label>
      </div>

      <!-- 状态消息 -->
      <div v-if="message" class="message" :class="{ 'error': message.includes('失败') }">
        {{ message }}
      </div>
    </div>
  </div>
</template>

<style scoped>
* {
  box-sizing: border-box;
}

.app {
  min-height: 100vh;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
  position: relative;
}

/* 顶部域名选择栏 */
.top-bar {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
}

.domain-dropdown {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  min-width: 150px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.domain-dropdown:focus {
  outline: none;
  border-color: #4285f4;
  box-shadow: 0 2px 8px rgba(66, 133, 244, 0.2);
}

.container {
  max-width: 500px;
  margin: 0 auto;
  padding: 60px 24px 24px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 邮箱和验证码框样式 */
.email-box, .code-box {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  position: relative;
}

.box-label {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 8px;
  font-weight: 500;
}

.box-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 40px;
  margin-bottom: 12px;
}

.email-text {
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 16px;
  color: #495057;
  flex: 1;
  word-break: break-all;
}

/* 复制按钮 */
.copy-btn {
  padding: 6px 8px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  color: #6c757d;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.copy-btn:hover {
  background: #e9ecef;
  color: #495057;
}

/* 操作按钮 */
.action-btn {
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.get-email-btn {
  background: #28a745;
  color: white;
}

.get-email-btn:hover:not(:disabled) {
  background: #218838;
}

.get-code-btn {
  background: #007bff;
  color: white;
}

.get-code-btn:hover:not(:disabled) {
  background: #0056b3;
}

.action-btn:disabled {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

/* 加载动画 */
.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 自动获取开关 */
.auto-toggle-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.auto-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
}

.auto-toggle input[type="checkbox"] {
  margin: 0;
  transform: scale(1.2);
}

.toggle-text {
  font-weight: 500;
}

.status-indicator {
  display: flex;
  align-items: center;
  margin-left: 8px;
}

.pulse-dot {
  width: 8px;
  height: 8px;
  background: #28a745;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

/* 消息提示 */
.message {
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;
  text-align: center;
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background: #f8d7da;
  color: #721c24;
  border-color: #f5c6cb;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .top-bar {
    position: static;
    text-align: center;
    margin-bottom: 20px;
  }

  .container {
    padding: 20px 16px;
  }

  .domain-dropdown {
    width: 100%;
    max-width: 200px;
  }
}
</style>
