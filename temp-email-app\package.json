{"name": "temp-email-app", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@tauri-apps/api": "^2", "@tauri-apps/plugin-clipboard-manager": "^2.3.0", "@tauri-apps/plugin-http": "^2.5.1", "@tauri-apps/plugin-opener": "^2", "lucide-vue-next": "^0.536.0", "vue": "^3.5.13"}, "devDependencies": {"@tauri-apps/cli": "^2", "@vitejs/plugin-vue": "^5.2.1", "typescript": "~5.6.2", "vite": "^6.0.3", "vue-tsc": "^2.1.10"}}