cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=C:\Users\<USER>\Desktop\email\temp-email-app\src-tauri\target\debug\build\tauri-plugin-opener-dc0e3a0ebce34707\out\tauri-plugin-opener-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=C:\Users\<USER>\Desktop\email\temp-email-app\src-tauri\target\debug\build\tauri-plugin-opener-dc0e3a0ebce34707\out\global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=\\?\C:\Users\<USER>\.cargo\registry\src\index.crates.io-1949cf8c6b5b557f\tauri-plugin-opener-2.2.3\api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
