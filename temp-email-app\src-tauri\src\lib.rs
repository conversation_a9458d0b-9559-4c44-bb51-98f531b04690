use serde::{Deserialize, Serialize};
use rand::Rng;
use regex::Regex;

// API响应结构体
#[derive(Debug, Serialize, Deserialize)]
struct ApiResponse<T> {
    code: i32,
    data: T,
    msg: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct Domain {
    id: i32,
    domain: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct DomainsData {
    domains: Vec<Domain>,
}

#[derive(Debug, Serialize, Deserialize)]
struct EmailData {
    email: String,
    #[serde(rename = "isCustomize")]
    is_customize: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct EmailInfo {
    id: i32,
    subject: String,
    #[serde(rename = "messageId")]
    message_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct EmailsData {
    emails: Vec<EmailInfo>,
    total: i32,
}

#[derive(Debug, Serialize, Deserialize)]
struct EmailDetail {
    #[serde(rename = "textContent")]
    text_content: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct EmailDetailData {
    #[serde(rename = "emailDetail")]
    email_detail: EmailDetail,
}

// 生成随机用户名（4-8位字母数字组合）
#[tauri::command]
fn generate_random_username() -> String {
    let mut rng = rand::thread_rng();
    let length = rng.gen_range(4..=8);
    let chars: Vec<char> = "abcdefghijklmnopqrstuvwxyz0123456789".chars().collect();

    (0..length)
        .map(|_| chars[rng.gen_range(0..chars.len())])
        .collect()
}

// 获取域名列表
#[tauri::command]
async fn get_domains() -> Result<Vec<Domain>, String> {
    let client = reqwest::Client::new();
    let response = client
        .get("https://temp-email-api.sitestage.top/v1/domains")
        .header("Authorization", "Bearer cxj12345")
        .send()
        .await
        .map_err(|e| format!("请求失败: {}", e))?;

    let api_response: ApiResponse<DomainsData> = response
        .json()
        .await
        .map_err(|e| format!("解析响应失败: {}", e))?;

    if api_response.code == 200 {
        Ok(api_response.data.domains)
    } else {
        Err(format!("API错误: {}", api_response.msg))
    }
}

// 创建邮箱
#[tauri::command]
async fn create_email(name: String, domain_id: i32) -> Result<String, String> {
    let client = reqwest::Client::new();

    // 创建正确的JSON payload
    let payload = serde_json::json!({
        "name": name,
        "domainId": domain_id
    });

    let response = client
        .post("https://temp-email-api.sitestage.top/v1/email")
        .header("Authorization", "Bearer cxj12345")
        .header("Content-Type", "application/json")
        .json(&payload)
        .send()
        .await
        .map_err(|e| format!("请求失败: {}", e))?;

    // 先获取响应文本进行调试
    let response_text = response.text().await
        .map_err(|e| format!("读取响应失败: {}", e))?;

    println!("API响应: {}", response_text);

    // 尝试解析JSON
    let api_response: ApiResponse<EmailData> = serde_json::from_str(&response_text)
        .map_err(|e| format!("解析响应失败: {} - 响应内容: {}", e, response_text))?;

    if api_response.code == 200 {
        Ok(api_response.data.email)
    } else {
        Err(format!("API错误: {}", api_response.msg))
    }
}

// 获取邮件列表
#[tauri::command]
async fn get_messages(email: String) -> Result<Vec<EmailInfo>, String> {
    let client = reqwest::Client::new();
    let url = format!(
        "https://temp-email-api.sitestage.top/v1/messages?limit=100&offset=1&email={}",
        email
    );

    let response = client
        .get(&url)
        .header("Authorization", "Bearer cxj12345")
        .send()
        .await
        .map_err(|e| format!("请求失败: {}", e))?;

    let api_response: ApiResponse<EmailsData> = response
        .json()
        .await
        .map_err(|e| format!("解析响应失败: {}", e))?;

    if api_response.code == 200 {
        Ok(api_response.data.emails)
    } else {
        Err(format!("API错误: {}", api_response.msg))
    }
}

// 获取邮件内容并提取验证码
#[tauri::command]
async fn get_verification_code(message_id: String) -> Result<String, String> {
    let client = reqwest::Client::new();
    let encoded_message_id = urlencoding::encode(&message_id);
    let url = format!(
        "https://temp-email-api.sitestage.top/v1/messages/{}",
        encoded_message_id
    );

    let response = client
        .get(&url)
        .header("Authorization", "Bearer cxj12345")
        .send()
        .await
        .map_err(|e| format!("请求失败: {}", e))?;

    let api_response: ApiResponse<EmailDetailData> = response
        .json()
        .await
        .map_err(|e| format!("解析响应失败: {}", e))?;

    if api_response.code == 200 {
        let text_content = api_response.data.email_detail.text_content;
        extract_verification_code(&text_content)
    } else {
        Err(format!("API错误: {}", api_response.msg))
    }
}

// 从邮件内容中提取验证码
fn extract_verification_code(content: &str) -> Result<String, String> {
    // 常见的验证码模式
    let patterns = vec![
        r"验证码[：:\s]*(\d{4,8})",
        r"verification code[：:\s]*(\d{4,8})",
        r"code[：:\s]*(\d{4,8})",
        r"(\d{4,8})",  // 最后尝试匹配4-8位数字
    ];

    for pattern in patterns {
        if let Ok(re) = Regex::new(pattern) {
            if let Some(captures) = re.captures(content) {
                if let Some(code) = captures.get(1) {
                    return Ok(code.as_str().to_string());
                }
            }
        }
    }

    Err("未找到验证码".to_string())
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_clipboard_manager::init())
        .invoke_handler(tauri::generate_handler![
            generate_random_username,
            get_domains,
            create_email,
            get_messages,
            get_verification_code
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
