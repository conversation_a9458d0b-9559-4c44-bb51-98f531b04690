# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-is-always-on-top"
description = "Enables the is_always_on_top command without any pre-configured scope."
commands.allow = ["is_always_on_top"]

[[permission]]
identifier = "deny-is-always-on-top"
description = "Denies the is_always_on_top command without any pre-configured scope."
commands.deny = ["is_always_on_top"]
