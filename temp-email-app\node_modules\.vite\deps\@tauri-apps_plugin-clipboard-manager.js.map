{"version": 3, "sources": ["../../@tauri-apps/api/image.js", "../../@tauri-apps/plugin-clipboard-manager/dist-js/index.js"], "sourcesContent": ["import { Resource, invoke } from './core.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/** An RGBA Image in row-major order from top to bottom. */\nclass Image extends Resource {\n    /**\n     * Creates an Image from a resource ID. For internal use only.\n     *\n     * @ignore\n     */\n    constructor(rid) {\n        super(rid);\n    }\n    /** Creates a new Image using RGBA data, in row-major order from top to bottom, and with specified width and height. */\n    static async new(rgba, width, height) {\n        return invoke('plugin:image|new', {\n            rgba: transformImage(rgba),\n            width,\n            height\n        }).then((rid) => new Image(rid));\n    }\n    /**\n     * Creates a new image using the provided bytes by inferring the file format.\n     * If the format is known, prefer [@link Image.fromPngBytes] or [@link Image.fromIcoBytes].\n     *\n     * Only `ico` and `png` are supported (based on activated feature flag).\n     *\n     * Note that you need the `image-ico` or `image-png` Cargo features to use this API.\n     * To enable it, change your Cargo.toml file:\n     * ```toml\n     * [dependencies]\n     * tauri = { version = \"...\", features = [\"...\", \"image-png\"] }\n     * ```\n     */\n    static async fromBytes(bytes) {\n        return invoke('plugin:image|from_bytes', {\n            bytes: transformImage(bytes)\n        }).then((rid) => new Image(rid));\n    }\n    /**\n     * Creates a new image using the provided path.\n     *\n     * Only `ico` and `png` are supported (based on activated feature flag).\n     *\n     * Note that you need the `image-ico` or `image-png` Cargo features to use this API.\n     * To enable it, change your Cargo.toml file:\n     * ```toml\n     * [dependencies]\n     * tauri = { version = \"...\", features = [\"...\", \"image-png\"] }\n     * ```\n     */\n    static async fromPath(path) {\n        return invoke('plugin:image|from_path', { path }).then((rid) => new Image(rid));\n    }\n    /** Returns the RGBA data for this image, in row-major order from top to bottom.  */\n    async rgba() {\n        return invoke('plugin:image|rgba', {\n            rid: this.rid\n        }).then((buffer) => new Uint8Array(buffer));\n    }\n    /** Returns the size of this image.  */\n    async size() {\n        return invoke('plugin:image|size', { rid: this.rid });\n    }\n}\n/**\n * Transforms image from various types into a type acceptable by Rust.\n *\n * See [tauri::image::JsImage](https://docs.rs/tauri/2/tauri/image/enum.JsImage.html) for more information.\n * Note the API signature is not stable and might change.\n */\nfunction transformImage(image) {\n    const ret = image == null\n        ? null\n        : typeof image === 'string'\n            ? image\n            : image instanceof Image\n                ? image.rid\n                : image;\n    return ret;\n}\n\nexport { Image, transformImage };\n", "import { invoke } from '@tauri-apps/api/core';\nimport { transformImage, Image } from '@tauri-apps/api/image';\n\n// Copyright 2019-2023 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * Read and write to the system clipboard.\n *\n * @module\n */\n/**\n * Writes plain text to the clipboard.\n * @example\n * ```typescript\n * import { writeText, readText } from '@tauri-apps/plugin-clipboard-manager';\n * await writeText('<PERSON><PERSON> is awesome!');\n * assert(await readText(), '<PERSON><PERSON> is awesome!');\n * ```\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 2.0.0\n */\nasync function writeText(text, opts) {\n    await invoke('plugin:clipboard-manager|write_text', {\n        label: opts?.label,\n        text\n    });\n}\n/**\n * Gets the clipboard content as plain text.\n * @example\n * ```typescript\n * import { readText } from '@tauri-apps/plugin-clipboard-manager';\n * const clipboardText = await readText();\n * ```\n * @since 2.0.0\n */\nasync function readText() {\n    return await invoke('plugin:clipboard-manager|read_text');\n}\n/**\n * Writes image buffer to the clipboard.\n *\n * #### Platform-specific\n *\n * - **Android / iOS:** Not supported.\n *\n * @example\n * ```typescript\n * import { writeImage } from '@tauri-apps/plugin-clipboard-manager';\n * const buffer = [\n *   // A red pixel\n *   255, 0, 0, 255,\n *\n *  // A green pixel\n *   0, 255, 0, 255,\n * ];\n * await writeImage(buffer);\n * ```\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 2.0.0\n */\nasync function writeImage(image) {\n    await invoke('plugin:clipboard-manager|write_image', {\n        image: transformImage(image)\n    });\n}\n/**\n * Gets the clipboard content as Uint8Array image.\n *\n * #### Platform-specific\n *\n * - **Android / iOS:** Not supported.\n *\n * @example\n * ```typescript\n * import { readImage } from '@tauri-apps/plugin-clipboard-manager';\n *\n * const clipboardImage = await readImage();\n * const blob = new Blob([await clipboardImage.rgba()], { type: 'image' })\n * const url = URL.createObjectURL(blob)\n * ```\n * @since 2.0.0\n */\nasync function readImage() {\n    return await invoke('plugin:clipboard-manager|read_image').then((rid) => new Image(rid));\n}\n/**\n * * Writes HTML or fallbacks to write provided plain text to the clipboard.\n *\n * #### Platform-specific\n *\n * - **Android / iOS:** Not supported.\n *\n * @example\n * ```typescript\n * import { writeHtml } from '@tauri-apps/plugin-clipboard-manager';\n * await writeHtml('<h1>Tauri is awesome!</h1>', 'plaintext');\n * // The following will write \"<h1>Tauri is awesome</h1>\" as plain text\n * await writeHtml('<h1>Tauri is awesome!</h1>', '<h1>Tauri is awesome</h1>');\n * // we can read html data only as a string so there's just readText(), no readHtml()\n * assert(await readText(), '<h1>Tauri is awesome!</h1>');\n * ```\n *\n * @returns A promise indicating the success or failure of the operation.\n *\n * @since 2.0.0\n */\nasync function writeHtml(html, altText) {\n    await invoke('plugin:clipboard-manager|write_html', {\n        html,\n        altText\n    });\n}\n/**\n * Clears the clipboard.\n *\n * #### Platform-specific\n *\n * - **Android:** Only supported on SDK 28+. For older releases we write an empty string to the clipboard instead.\n *\n * @example\n * ```typescript\n * import { clear } from '@tauri-apps/plugin-clipboard-manager';\n * await clear();\n * ```\n * @since 2.0.0\n */\nasync function clear() {\n    await invoke('plugin:clipboard-manager|clear');\n}\n\nexport { clear, readImage, readText, writeHtml, writeImage, writeText };\n"], "mappings": ";;;;;;;AAMA,IAAM,QAAN,MAAM,eAAc,SAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB,YAAY,KAAK;AACb,UAAM,GAAG;AAAA,EACb;AAAA;AAAA,EAEA,aAAa,IAAI,MAAM,OAAO,QAAQ;AAClC,WAAO,OAAO,oBAAoB;AAAA,MAC9B,MAAM,eAAe,IAAI;AAAA,MACzB;AAAA,MACA;AAAA,IACJ,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,OAAM,GAAG,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,aAAa,UAAU,OAAO;AAC1B,WAAO,OAAO,2BAA2B;AAAA,MACrC,OAAO,eAAe,KAAK;AAAA,IAC/B,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,OAAM,GAAG,CAAC;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,aAAa,SAAS,MAAM;AACxB,WAAO,OAAO,0BAA0B,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,QAAQ,IAAI,OAAM,GAAG,CAAC;AAAA,EAClF;AAAA;AAAA,EAEA,MAAM,OAAO;AACT,WAAO,OAAO,qBAAqB;AAAA,MAC/B,KAAK,KAAK;AAAA,IACd,CAAC,EAAE,KAAK,CAAC,WAAW,IAAI,WAAW,MAAM,CAAC;AAAA,EAC9C;AAAA;AAAA,EAEA,MAAM,OAAO;AACT,WAAO,OAAO,qBAAqB,EAAE,KAAK,KAAK,IAAI,CAAC;AAAA,EACxD;AACJ;AAOA,SAAS,eAAe,OAAO;AAC3B,QAAM,MAAM,SAAS,OACf,OACA,OAAO,UAAU,WACb,QACA,iBAAiB,QACb,MAAM,MACN;AACd,SAAO;AACX;;;AC1DA,eAAe,UAAU,MAAM,MAAM;AACjC,QAAM,OAAO,uCAAuC;AAAA,IAChD,OAAO,6BAAM;AAAA,IACb;AAAA,EACJ,CAAC;AACL;AAUA,eAAe,WAAW;AACtB,SAAO,MAAM,OAAO,oCAAoC;AAC5D;AAyBA,eAAe,WAAW,OAAO;AAC7B,QAAM,OAAO,wCAAwC;AAAA,IACjD,OAAO,eAAe,KAAK;AAAA,EAC/B,CAAC;AACL;AAkBA,eAAe,YAAY;AACvB,SAAO,MAAM,OAAO,qCAAqC,EAAE,KAAK,CAAC,QAAQ,IAAI,MAAM,GAAG,CAAC;AAC3F;AAsBA,eAAe,UAAU,MAAM,SAAS;AACpC,QAAM,OAAO,uCAAuC;AAAA,IAChD;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAeA,eAAe,QAAQ;AACnB,QAAM,OAAO,gCAAgC;AACjD;", "names": []}